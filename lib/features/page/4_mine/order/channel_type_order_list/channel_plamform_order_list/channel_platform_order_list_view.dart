import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/order/GSDateTabBarWidget.dart';
import 'package:wd/shared/widgets/order/order_sub_cell.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/utils/screenUtil.dart';

import 'channel_platform_order_list_cubit.dart';
import 'channel_platform_order_list_state.dart';

class ChannelPlatformOrderListPage extends BasePage {
  final String gameClassCode;
  final String categoryCode;
  final String thirdPlatformId;
  final String gameClassName;
  final RecordDateType dateType;

  const ChannelPlatformOrderListPage({
    super.key,
    required this.gameClassCode,
    required this.categoryCode,
    required this.thirdPlatformId,
    required this.gameClassName,
    required this.dateType,
  });

  @override
  BasePageState<BasePage> getState() => _ChannelPlamformOrderListPageState();
}

class _ChannelPlamformOrderListPageState extends BasePageState<ChannelPlatformOrderListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  Color cardBgColor = const Color(0xFFF7F7F7);

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = widget.gameClassName;
    isNeedEmptyDataWidget = false;

    _tabController = TabController(length: 3, vsync: this, initialIndex: widget.dateType.index)
      ..addListener(() {
        _getData();
      });
    _getData();
  }

  void _onLoading() {
    context.read<ChannelPlatformOrderListCubit>().updatePageNoToNext();
    _getData();
  }

  _getData() {
    context.read<ChannelPlatformOrderListCubit>().currentDateTypeChanged(
          type: RecordDateType.values[_tabController.index],
          categoryCode: widget.categoryCode,
          gameClassCode: widget.gameClassCode,
          thirdPlatformId: widget.thirdPlatformId,
        );
  }

  @override
  void dispose() {
    _tabController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  /// 下拉刷新
  void _onRefresh() {
    _getData();
  }

  Widget mainPageWidget(ChannelPlatformOrderListState state) {
    return GSDateTabBarWidget(
      tabController: _tabController,
      children: [
        if (state.netState == NetState.emptyDataState) ...[
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                emptyWidget('empty_popular'.tr()),
              ],
            ),
          )
        ],
        if (state.netState == NetState.dataSuccessState) ...[
          Container(
            margin: EdgeInsets.only(top: 10.gw),
            padding: EdgeInsets.symmetric(horizontal: 15.gw),
            color: cardBgColor,
            height: 30.gw,
            alignment: Alignment.centerLeft,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('下单金额：', style: Theme.of(context).textTheme.bodyLarge),
                BlocBuilder<ChannelPlatformOrderListCubit, ChannelPlatformOrderListState>(
                  builder: (context, state) {
                    return Text(
                      state.totalBetAmount.formattedMoney,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontFamily: 'WeChatSansStd',
                            // color: state.totalBetAmount >= 0
                            //     ? const Color(0xffe23f3b)
                            //     : const Color(0xff67ac5c),
                          ),
                    );
                  },
                ),
                const Spacer(),
                Text('盈亏总额：', style: Theme.of(context).textTheme.bodyLarge),
                BlocBuilder<ChannelPlatformOrderListCubit, ChannelPlatformOrderListState>(
                  builder: (context, state) {
                    return Text(
                      state.totalWinAmount.formattedMoney,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontFamily: 'WeChatSansStd',
                            color: state.totalWinAmount >= 0 ? const Color(0xff67ac5c) : const Color(0xffe23f3b),
                          ),
                    );
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: AnimationLimiter(
              child: CommonRefresher(
                bgColor: cardBgColor,
                enablePullDown: true,
                enablePullUp: true,
                refreshController: refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                listWidget: ListView.separated(
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  itemBuilder: (context, index) => AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: GSOrderSubListCell(model: state.dataList![index]),
                      ),
                    ),
                  ),
                  separatorBuilder: (_, __) => SizedBox(height: 10.gw),
                  itemCount: state.dataList!.length,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _listener(BuildContext context, ChannelPlatformOrderListState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<ChannelPlatformOrderListCubit, ChannelPlatformOrderListState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
