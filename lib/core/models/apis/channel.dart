import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/models/entities/game_login_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/order_channel_list_entity.dart';
import 'package:wd/core/models/entities/order_main_entity.dart';
import 'package:wd/core/models/entities/platform_amount_entity.dart';
import 'package:wd/core/models/entities/winner_entity.dart';
import 'package:wd/core/models/entities/winning_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/http/https.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:wd/injection_container.dart';

enum ThirdPlatformType { main, sub } // 三方游戏平台类型 main=主 sub=下级

enum AmountTransferType { into, out } // 三方游戏额度转换类型 into=上分 out=下分

class ChannelApi {
  /// 获取游戏列表
  // static Future<({String? errorStr, List<GameType> game, Map<String, String> platformMap})> fetchGameList() async {
  //   ResponseModel res = await Http().request<GameListEntity>(
  //     ApiConstants.gameListWithPlatformInfo,
  //     needSignIn: false,
  //     needShowToast: false,
  //   );
  //   if (res.isSuccess && res.data != null) {
  //     List<GameType> gameList = res.data.gameList;
  //     List<PlatformInfo> thirdPlatformList = res.data.thirdPlatformList;
  //     Map<String, String> map = {for (var e in thirdPlatformList) e.platformCode: e.iconUrl};
  //     return (errorStr: null, game: gameList, platformMap: map);
  //   } else {
  //     return (errorStr: res.msg, game: <GameType>[], platformMap: <String, String>{});
  //   }
  // }

  static Future<List<GameTypeV2>> fetchGameListV2() async {
    ResponseModel res = await Http().request<GameTypeV2ListEntity>(
      method: HttpMethod.get,
      ApiConstants.gameListV2,
      needSignIn: false,
      needShowToast: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    } else {
      return [];
    }
  }

  static Future<List<GameV2>> fetchPlatformGameListV2(gameClassCode, platformId) async {
    ResponseModel res = await Http().request<GameV2ListEntity>(
      method: HttpMethod.get,
      '${ApiConstants.platformGameList}/$gameClassCode/$platformId',
      needSignIn: false,
      needShowToast: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    } else {
      return [];
    }
  }

  /// 获取游戏收藏列表
  static Future<List<GameV2>> fetchFavGameList() async {
    ResponseModel res = await Http().request<GameV2ListEntity>(
      ApiConstants.favGameList,
      method: HttpMethod.get,
      needShowToast: false,
    );
    return res.data.list;
  }

  /// 获取游戏登录链接
  static Future<GameLoginEntity?> fetchGameLoginUrl({
    required int gameId,
    required int thirdPlatformId,
  }) async {
    ResponseModel? res = await Http().request<GameLoginEntity>(
      ApiConstants.gameLogin,
      params: {
        "gameId": gameId,
        "device": SystemUtil.isWeb() ? 1 : 2,
        "platformId": thirdPlatformId,
        'returnUrl': 'http://127.0.0.1:8082',
        'lang': '',
      },
    );

    if (res.isSuccess && res.data != null) {
      sl<UserCubit>().addPlatformId(thirdPlatformId);
      return res.data;
    }
    return null;
  }

  /// 获取三方游戏余额
  static Future<double?> getPlatformAmount({required int platformId}) async {
    ResponseModel? res = await Http().request<double>(
      ApiConstants.gameAmount,
      params: {"platformId": platformId},
      isFormUrlEncoded: true,
    );
    if (res.isSuccess) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 获取三方游戏余额
  static Future<List<PlatformAmountEntity>> getAllPlatformAmount() async {
    ResponseModel? res = await Http().request<PlatformAmountEntityList>(
      ApiConstants.gameAmountAll,
      isFormUrlEncoded: true,
    );
    if (res.isSuccess && res.data != null) {
      return res.data.list;
    } else {
      return [];
    }
  }

  /// 额度转换三方平台（AmountTransferType：.into=上分 .out=下分）
  static Future<bool> transferGameAmount(AmountTransferType type,
      {required double amount, required int platformId}) async {
    ResponseModel? res = await Http().request<dynamic>(
      type == AmountTransferType.into ? ApiConstants.gameAmountTransferTo : ApiConstants.gameAmountTransferOut,
      params: {
        "amount": amount,
        "platformId": platformId,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 一键下分
  static Future<bool> transferOutAllGameAmount() async {
    ResponseModel? res = await Http().request<dynamic>(
      ApiConstants.gameAmountAllTransferOut,
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }

  /// 查看所有类型游戏注单统计记录
  static Future<List<OrderMainEntity>> gameAllTypeRecord({required RecordDateType type}) async {
    return [
      OrderMainEntity()
        ..gameClassCode = "SX"
        ..gameClassName = "真人"
        ..rebate = 1000
        ..totalBetAmount = 1000
        ..totalWin = 1000
        ..totalSendAmount = 1000,
      OrderMainEntity()
        ..gameClassCode = "SX"
        ..gameClassName = "真人"
        ..rebate = 1000
        ..totalBetAmount = 1000
        ..totalWin = 1000
        ..totalSendAmount = 1000,
      OrderMainEntity()
        ..gameClassCode = "SX"
        ..gameClassName = "真人"
        ..rebate = 1000
        ..totalBetAmount = 1000
        ..totalWin = 1000
        ..totalSendAmount = 1000,
    ];
    ResponseModel? res = await Http().request<OrderMainEntityList>(
      ApiConstants.gameAllTypeRecord,
      params: {
        "queryDateType": type.typeName,
      },
      isFormUrlEncoded: true,
    );

    if (res.isSuccess) {
      final list = res.data.list;
      return list;
    } else {
      return [];
    }
  }

  /// 查看所有类型游戏注单统计记录
  static Future<List<OrderPlatformEntity>> gameOneTypeRecord(
      {required RecordDateType type, required String gameClassCode}) async {
    ResponseModel? res = await Http().request<OrderPlatformEntityList>(
      ApiConstants.gameOneTypeRecord,
      params: {
        "queryDateType": type.typeName,
        "gameClassCode": gameClassCode,
      },
      isFormUrlEncoded: true,
    );
    return [
      OrderPlatformEntity()
        ..categoryCode = "DZ"
        ..gameClassCode = "SX"
        ..gameClassName = "真人"
        ..platFormName = "真人"
        ..rebate = 1000
        ..totalBetAmount = 1000
        ..totalWin = 1000
        ..totalSendAmount = 1000,
      OrderPlatformEntity()
        ..categoryCode = "DZ"
        ..gameClassCode = "SX"
        ..gameClassName = "真人"
        ..platFormName = "真人"
        ..rebate = 1000
        ..totalBetAmount = 1000
        ..totalWin = 1000
        ..totalSendAmount = 1000,
    ];
    if (res.isSuccess) {
      final list = res.data.list;
      return list;
    } else {
      return [];
    }
  }

  /// 查看平台游戏记录
  static Future<OrderChannelListEntity?> fetchGameMainPlatformRecord({
    required String gameClassCode,
    required String thirdPlatformId,
    required String categoryCode,
    required int startDate,
    required int endDate,
    int? pageNo,
    int? pageSize,
  }) async {
    ResponseModel? res = await Http().request<OrderChannelListEntity>(
      ApiConstants.gameMainPlatformRecord,
      params: {
        "gameClassCode": gameClassCode,
        "thirdPlatformId": thirdPlatformId,
        "categoryCode": categoryCode,
        "startDate": startDate,
        "endDate": endDate,
        if (pageNo != null) "pageNo": pageNo,
        if (pageSize != null) "pageSize": pageSize
      },
    );
    return OrderChannelListEntity()
      ..totalBetAmount = 10000
      ..totalWinToday = 10000
      ..totalSendAmount = 10000
      ..totalRevenue = 10000
      ..page = OrderChannelListPage()
      ..page.records = [
        OrderChannelListPageRecords()
          ..id = 1
          ..channelUniqueId = "123456"
          ..thirdPlatformId = 123456
          ..thirdPlatformName = "AG"
          ..userId = 123456
          ..userNo = "123456"
          ..agentId = 123456
          ..generalAgentId = 123456
          ..gameClassCode = "SX"
          ..gameClassName = "真人"
          ..categoryCode = "XY"
          ..categoryName = "XY"
          ..gameCode = "1"
          ..gameName = "1"
          ..roomName = "1"
          ..tableName = "1"
          ..sendAmount = 10000
          ..winAmount = 10000
          ..betAmount = 10000
          ..revenue = 10000
          ..matchNumber = "1"
          ..returnRate = 10000
          ..returnAmount = 10000
          ..gameResult = 2
          ..recordOriginal = "1"
          ..pullTime = "1643728800000",
      ]
      ..page.total = 2
      ..page.size = 1
      ..page.current = 01
      ..page.pages = 1;

    if (res.isSuccess) {
      final model = res.data;
      return model;
    } else {
      return null;
    }
  }

  /// 查看平台游戏记录
  static Future<String?> fetchGameSubPlatformRecord(ThirdPlatformType type,
      {required int thirdPlatformId,
      required String gameClassCode,
      required String categoryCode,
      required String startDate,
      required String endDate}) async {
    ResponseModel? res = await Http().request<dynamic>(
      type == ThirdPlatformType.main ? ApiConstants.gameMainPlatformRecord : ApiConstants.gameSubPlatformRecord,
      params: {
        "userNo": sl<UserCubit>().state.userInfo?.userNo,
        "thirdPlatformId": thirdPlatformId,
        "gameClassCode": gameClassCode,
        "categoryCode": categoryCode,
        "startDate": startDate,
        "endDate": endDate,
      },
    );

    if (res.isSuccess) {
      return null;
    } else {
      return res.msg;
    }
  }

  /// 查看所有类型游戏注单统计记录
  static Future<List<WinningEntity>> fetchWinningList() async {
    ResponseModel? res = await Http().request<WinningEntityList>(
      ApiConstants.winningList,
      isFormUrlEncoded: true,
      needSignIn: false,
      needShowToast: false,
    );
    if (res.isSuccess) {
      return res.data.list;
    } else {
      return [];
    }
  }

  /// 查询最新中奖名单
  static Future<List<WinnerEntity>> fetchWinnerList() async {
    ResponseModel? res = await Http().request<WinnerEntityList>(
      ApiConstants.gameWinners,
      method: HttpMethod.get,
      isFormUrlEncoded: true,
      needSignIn: false,
      needShowToast: false,
    );
    if (res.isSuccess) {
      return res.data.list;
    } else {
      return [];
    }
  }

  /// 用户点击 收藏/取消收藏 游戏
  static Future<bool> operateGameFav({
    required bool isFav,
    required int gameId,
    required int gameType,
  }) async {
    ResponseModel? res = await Http().request(
      isFav ? ApiConstants.submitGameFav : ApiConstants.cancelGameFav,
      method: HttpMethod.get,
      params: {
        'gameId': gameId,
        'gameType': gameType,
      },
      isFormUrlEncoded: true,
    );
    return res.isSuccess;
  }
}
